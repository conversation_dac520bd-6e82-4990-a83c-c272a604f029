import { Database } from '@/types_db';
import { SupabaseClient } from '@supabase/supabase-js';
import { cache } from 'react';

export const getUser = cache(async (supabase: SupabaseClient) => {
  const {
    data: { user }
  } = await supabase.auth.getUser();
  return user;
});

export const getSubscription = cache(async (supabase: SupabaseClient) => {
  const { data: subscription, error } = await supabase
    .from('subscriptions')
    .select('*, prices(*, products(*))')
    .in('status', ['trialing', 'active'])
    .maybeSingle();

  return subscription;
});
/*

SELECT o.*
FROM organizations o
JOIN organization_memberships m ON o.id = m.organization_id
WHERE m.user_id = auth.uid();
*/
interface Organization {
  id: string;
  name: string;
}

interface OrganizationMembershipRow {
  organization: Organization
}



export const getUserOrganizations = cache( async (supabase: SupabaseClient) => {

  const {
    data: { user },
    error: userError,
  } = await supabase.auth.getUser()

  if (userError || !user) {
    console.error('No user found:', userError)
    return []
  }

  const { data, error } = await supabase
    .from('organization_memberships')
    .select<string, OrganizationMembershipRow>
    ('organization:organization_id(id, name)')
    .eq('user_id', user.id)

    console.log(data)

  if (error) {
    console.error('Error fetching organizations:', error)
    return []
  }
const organizations = data.map((entry) => entry.organization)
console.log(organizations)
  return organizations// [{ id, name }, ...]
}
);


export const getProducts = cache(async (supabase: SupabaseClient) => {
  const { data: products, error } = await supabase
    .from('products')
    .select('*, prices(*)')
    .eq('active', true)
    .eq('prices.active', true)
    .order('metadata->index')
    .order('unit_amount', { referencedTable: 'prices' });

  return products;
});

export const getUserDetails = cache(async (supabase: SupabaseClient) => {
  const { data: userDetails } = await supabase
    .from('users')
    .select('*')
    .single();
  return userDetails;
});

export const getOrganizationAgents = cache(async (
  supabase: SupabaseClient,
  organizationId: string,
  searchQuery?: string,
  page: number = 1,
  pageSize: number = 10
) => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;

  // First, get the agents with their team information
  let query = supabase
    .from('agents')
    .select(`
      *,
      team:team_id (id, name)
    `, { count: 'exact' })
    .eq('organization_id', organizationId)
    .range(start, end);

  if (searchQuery) {
    query = query.ilike('name', `%${searchQuery}%`);
  }

  const { data: agents, count, error } = await query;

  if (error || !agents) {
    return {
      data: [],
      count: 0,
      error,
      hasMore: false
    };
  }

  // For each agent, get their budget usage from the agent_usage table
  const agentsWithBudgetUsage = await Promise.all(agents.map(async (agent) => {
    // Get the sum of usage costs for this agent
    const { data: usageData, error: usageError } = await supabase
      .from('agent_usage')
      .select('cost_cents')
      .eq('agent_id', agent.id)
      .gt('cost_cents', 0);

    // Calculate total usage
    let budget_used_cents = 0;
    if (!usageError && usageData && usageData.length > 0) {
      budget_used_cents = usageData.reduce((sum, usage) => sum + (usage.cost_cents || 0), 0);
    }

    return {
      ...agent,
      budget_used_cents
    };
  }));

  return {
    data: agentsWithBudgetUsage,
    count,
    error: null,
    hasMore: count ? start + pageSize < count : false
  };
});


export const getOrganizationTeams = cache(async (
  supabase: SupabaseClient,
  organizationId: string,
  searchQuery?: string,
  page: number = 1,
  pageSize: number = 10
) => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;

  // First, get the teams
  let query = supabase
    .from('agent_teams')
    .select('*', { count: 'exact' })
    .eq('organization_id', organizationId)
    .range(start, end);

  if (searchQuery) {
    query = query.ilike('name', `%${searchQuery}%`);
  }

  const { data: teams, count, error } = await query;

  if (error || !teams) {
    console.log(error);
    return {
      data: [],
      count: 0,
      error,
      hasMore: false
    };
  }

  // For each team, get the count of agents and budget usage
  const teamsWithDetails = await Promise.all(teams.map(async (team) => {
    // Get agent count
    const { count: agent_count } = await supabase
      .from('agents')
      .select('*', { count: 'exact', head: true })
      .eq('team_id', team.id);

    // Get agents in this team
    const { data: teamAgents } = await supabase
      .from('agents')
      .select('id')
      .eq('team_id', team.id);

    // Calculate budget usage for all agents in this team
    let budget_used_cents = 0;

    if (teamAgents && teamAgents.length > 0) {
      // Get all agent IDs in this team
      const agentIds = teamAgents.map(agent => agent.id);

      // Get usage data for all agents in this team
      const { data: usageData } = await supabase
        .from('agent_usage')
        .select('cost_cents')
        .in('agent_id', agentIds)
        .gt('cost_cents', 0);

      // Sum up the usage
      if (usageData && usageData.length > 0) {
        budget_used_cents = usageData.reduce((sum, usage) => sum + (usage.cost_cents || 0), 0);
      }
    }

    return {
      ...team,
      agent_count: agent_count || 0,
      budget_used_cents
    };
  }));

  return {
    data: teamsWithDetails,
    count,
    error: null,
    hasMore: count ? start + pageSize < count : false
  };
});
export const getOrganizationUsers = cache(async (
  supabase: SupabaseClient,
  organizationId: string,
  searchQuery?: string,
  page: number = 1,
  pageSize: number = 10
) => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;

  let query = supabase
    .from('organization_memberships')
    .select(`
      *,
      user:user_id (
        id,
        email,
        full_name,
        avatar_url,
        created_at,
        updated_at
      )
    `, { count: 'exact' })
    .eq('organization_id', organizationId)
    .range(start, end);

  if (searchQuery) {
    query = query.or(`user.email.ilike.%${searchQuery}%,user.full_name.ilike.%${searchQuery}%`);
  }

  const { data, count, error } = await query;

  return {
    data: data?.map(membership => ({
      ...membership.user,
      role: membership.role,
      member_since: membership.created_at
    })),
    count,
    error,
    hasMore: count ? start + pageSize < count : false
  };
});

export const getAgentDetails = cache(async (supabase: SupabaseClient, agentId: string) => {
  const { data: agent, error } = await supabase
  .from('agents')
  .select(`
    *,
    agent_configs(*)
  `)
  .eq('id', agentId)
  .single();

  if (error) {
    console.error('Error fetching agent details:', error);
    return null;
  }

  return agent;
});
export type Agent = Database['public']['Tables']['agents']['Row']

export const createAgent = cache(async (
  supabase: SupabaseClient,
  name: string,
  orgId: string,
  conversation_config: any,
  teamId?: string,
  budget?: number,
  elevenLabsAgentId?: string
): Promise<Agent> => {
  // If teamId is 'none' or undefined, set it to null
  const finalTeamId = teamId && teamId !== 'none' ? teamId : null;

  // Create agent data object without conversation_config
  const agentData: any = {
    name,
    organization_id: orgId,
    team_id: finalTeamId,
    ...(budget !== undefined && { budget_cents: Math.round(budget * 100) })
  };

  // First create the agent
  const { data: agent, error: agentError } = await supabase
    .from('agents')
    .insert([agentData])
    .select()
    .single();

  if (agentError) {
    console.error('Error creating agent:', agentError);
    throw agentError;
  }

  // Create multiple agent configs as needed
  if (conversation_config) {
    // Create configs array to hold all configs
    const configs = [];

    // 1. Create the main internal config
    const internalConfigData: any = {
      agent_id: agent.id,
      config_type: 'internal',
      config: conversation_config
    };
    configs.push(internalConfigData);

    // 2. Create the ElevenLabs config if we have an ElevenLabs agent ID
    if (elevenLabsAgentId) {
      const elevenLabsConfigData: any = {
        agent_id: agent.id,
        config_type: 'elevenlabs',
        external_provider_id: elevenLabsAgentId,
        // Include any ElevenLabs-specific config
        config: conversation_config || {}
      };
      configs.push(elevenLabsConfigData);
    }

    console.log('Creating agent configs:', JSON.stringify(configs, null, 2));

    // Insert all configs at once
    const { error: configError } = await supabase
      .from('agent_configs')
      .insert(configs);

    if (configError) {
      console.error('Error creating agent config:', configError);
      throw configError;
    }
  }

  return agent;
});

export const deleteAgent = cache(async (
  supabase: SupabaseClient,
  agentId: string
): Promise<boolean> => {
  try {
    // First delete related agent_configs
    const { error: configError } = await supabase
      .from('agent_configs')
      .delete()
      .eq('agent_id', agentId);

    if (configError) {
      console.error('Error deleting agent configs:', configError);
      throw configError;
    }

    // Then delete the agent
    const { error: agentError } = await supabase
      .from('agents')
      .delete()
      .eq('id', agentId);

    if (agentError) {
      console.error('Error deleting agent:', agentError);
      throw agentError;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteAgent:', error);
    throw error;
  }
});

export const duplicateAgent = cache(async (
  supabase: SupabaseClient,
  agentId: string,
  newName?: string
): Promise<Agent> => {
  try {
    // Get the original agent
    const { data: originalAgent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .single();

    if (agentError || !originalAgent) {
      console.error('Error fetching original agent:', agentError);
      throw agentError || new Error('Agent not found');
    }

    // Get the agent configs
    const { data: originalConfigs, error: configError } = await supabase
      .from('agent_configs')
      .select('*')
      .eq('agent_id', agentId);

    if (configError) {
      console.error('Error fetching agent configs:', configError);
      throw configError;
    }

    // Create a new agent with the same properties
    // Create a copy of the original agent without the id and created_at fields
    const { id, created_at, ...agentDataWithoutId } = originalAgent;

    const newAgentData = {
      ...agentDataWithoutId,
      name: newName || `${originalAgent.name} (Copy)`
    };

    // Insert the new agent
    const { data: newAgent, error: newAgentError } = await supabase
      .from('agents')
      .insert([newAgentData])
      .select()
      .single();

    if (newAgentError || !newAgent) {
      console.error('Error creating duplicate agent:', newAgentError);
      throw newAgentError || new Error('Failed to create duplicate agent');
    }

    // Create new configs for the duplicated agent
    if (originalConfigs && originalConfigs.length > 0) {
      const newConfigs = originalConfigs.map(config => {
        // Remove id and created_at fields
        const { id, created_at, ...configWithoutId } = config;

        // Handle different config types appropriately
        if (config.config_type === 'elevenlabs') {
          // For ElevenLabs configs, we need to create a new ElevenLabs agent
          // For now, just copy the config but don't copy the external_provider_id
          // A separate sync process should create a new ElevenLabs agent
          const { external_provider_id, ...configWithoutExternalId } = configWithoutId;

          return {
            ...configWithoutExternalId,
            agent_id: newAgent.id
          };
        } else if (config.config_type !== 'elevenlabs') {
          // For other voice providers, handle similarly
          const { external_provider_id, ...configWithoutExternalId } = configWithoutId;

          return {
            ...configWithoutExternalId,
            agent_id: newAgent.id
          };
        }

        return {
          ...configWithoutId,
          agent_id: newAgent.id
        };
      });

      const { error: newConfigError } = await supabase
        .from('agent_configs')
        .insert(newConfigs);

      if (newConfigError) {
        console.error('Error creating duplicate agent configs:', newConfigError);
        // If config creation fails, delete the new agent to avoid orphaned agents
        await supabase.from('agents').delete().eq('id', newAgent.id);
        throw newConfigError;
      }
    }

    return newAgent;
  } catch (error) {
    console.error('Error in duplicateAgent:', error);
    throw error;
  }
});

export const getOrganizationConversations = cache(async (
  supabase: SupabaseClient,
  organizationId: string,
  searchQuery?: string,
  page: number = 1,
  pageSize: number = 10
) => {
  const start = (page - 1) * pageSize;
  const end = start + pageSize - 1;

  // First, get all agents for this organization
  const { data: agents } = await supabase
    .from('agents')
    .select('id, name')
    .eq('organization_id', organizationId);

  if (!agents || agents.length === 0) {
    return {
      data: [],
      count: 0,
      error: null,
      hasMore: false
    };
  }

  // Get agent IDs
  const agentIds = agents.map(agent => agent.id);

  // Query conversations for these agents
  let query = supabase
    .from('conversations')
    .select(`
      *,
      agent:agent_id (id, name)
    `, { count: 'exact' })
    .in('agent_id', agentIds)
    .order('start_time', { ascending: false })
    .range(start, end);

  if (searchQuery) {
    // Join with agents to search by agent name
    query = query.or(`agent.name.ilike.%${searchQuery}%`);
  }

  const { data: conversations, count, error } = await query;

  if (error || !conversations) {
    console.error('Error fetching conversations:', error);
    return {
      data: [],
      count: 0,
      error,
      hasMore: false
    };
  }

  // For each conversation, get the message count
  const conversationsWithMessageCount = await Promise.all(
    conversations.map(async (conversation) => {
      const { count: messageCount } = await supabase
        .from('conversation_messages')
        .select('id', { count: 'exact' })
        .eq('conversation_id', conversation.id);

      return {
        id: conversation.id,
        timestamp: conversation.start_time,
        agent_name: conversation.agent?.name || 'Unknown Agent',
        agent_id: conversation.agent_id,
        duration_seconds: conversation.duration_secs || 0,
        message_count: messageCount || 0,
        status: conversation.success_status === 'success' ? 'successful' as const : 'failed' as const,
        cost_cents: conversation.cost_cents || 0
      };
    })
  );

  return {
    data: conversationsWithMessageCount,
    count,
    error: null,
    hasMore: count ? start + pageSize < count : false
  };
});

export const getConversationDetails = cache(async (
  supabase: SupabaseClient,
  conversationId: string
) => {
  // Get the conversation details
  const { data: conversation, error: conversationError } = await supabase
    .from('conversations')
    .select(`
      *,
      agent:agent_id (id, name)
    `)
    .eq('id', conversationId)
    .single();

  if (conversationError || !conversation) {
    console.error('Error fetching conversation details:', conversationError);
    return null;
  }

  // Get the conversation messages
  const { data: messages, error: messagesError } = await supabase
    .from('conversation_messages')
    .select('*')
    .eq('conversation_id', conversationId)
    .order('created_at', { ascending: true });

  if (messagesError) {
    console.error('Error fetching conversation messages:', messagesError);
    return null;
  }

  // Format the transcript
  const transcript = messages?.map(message => ({
    role: message.sender_type,
    content: message.message,
    timestamp: message.created_at
  })) || [];

  // Count the messages
  const messageCount = messages?.length || 0;

  return {
    id: conversation.id,
    timestamp: conversation.start_time,
    agent_name: conversation.agent?.name || 'Unknown Agent',
    agent_id: conversation.agent_id,
    duration_seconds: conversation.duration_secs || 0,
    message_count: messageCount,
    status: conversation.success_status === 'success' ? 'successful' as const : 'failed' as const,
    cost_cents: conversation.cost_cents || 0,
    transcript,
    summary: conversation.summary,
    metadata: conversation.metadata,
    analysis: conversation.analysis
  };
});

export const getDashboardStats = cache(async (
  supabase: SupabaseClient,
  organizationId: string
) => {
  // Get all agents for this organization
  const { data: agents } = await supabase
    .from('agents')
    .select('id, name')
    .eq('organization_id', organizationId);

  if (!agents || agents.length === 0) {
    return {
      totalCalls: 0,
      avgDurationSeconds: 0,
      avgCostCents: 0,
      totalCostCents: 0,
      callsByDate: [],
      recentConversations: []
    };
  }

  // Get agent IDs
  const agentIds = agents.map(agent => agent.id);

  // Get total number of calls
  const { count: totalCalls } = await supabase
    .from('conversations')
    .select('id', { count: 'exact', head: true })
    .in('agent_id', agentIds);

  // Get average duration
  const { data: durationData } = await supabase
    .from('conversations')
    .select('duration_secs')
    .in('agent_id', agentIds)
    .not('duration_secs', 'is', null);

  let avgDurationSeconds = 0;
  if (durationData && durationData.length > 0) {
    const totalDuration = durationData.reduce((sum, conv) => sum + (conv.duration_secs || 0), 0);
    avgDurationSeconds = Math.round(totalDuration / durationData.length);
  }

  // Get average cost
  const { data: costData } = await supabase
    .from('conversations')
    .select('cost_cents')
    .in('agent_id', agentIds)
    .not('cost_cents', 'is', null);

  let avgCostCents = 0;
  let totalCostCents = 0;
  if (costData && costData.length > 0) {
    totalCostCents = costData.reduce((sum, conv) => sum + (conv.cost_cents || 0), 0);
    avgCostCents = Math.round(totalCostCents / costData.length);
  }

  // Get calls by date for the chart (last 90 days)
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  const { data: callsByDateRaw } = await supabase
    .from('conversations')
    .select('start_time')
    .in('agent_id', agentIds)
    .gte('start_time', ninetyDaysAgo.toISOString())
    .order('start_time', { ascending: true });

  // Process the data for the chart
  const callsByDate = processCallsForChart(callsByDateRaw || []);

  // Get recent conversations
  const { data: recentConversations } = await supabase
    .from('conversations')
    .select(`
      id,
      start_time,
      duration_secs,
      cost_cents,
      success_status,
      agent:agent_id (id, name)
    `)
    .in('agent_id', agentIds)
    .order('start_time', { ascending: false })
    .limit(10);

  // Format recent conversations
  const formattedRecentConversations = (recentConversations || []).map(conv => ({
    id: conv.id,
    header: conv.agent && typeof conv.agent === 'object' ? (conv.agent as any).name || 'Unknown Agent' : 'Unknown Agent',
    type: 'Conversation',
    status: conv.success_status === 'success' ? 'Done' : 'Failed',
    timestamp: conv.start_time,
    duration: conv.duration_secs || 0,
    cost: conv.cost_cents || 0
  }));

  return {
    totalCalls: totalCalls || 0,
    avgDurationSeconds,
    avgCostCents,
    totalCostCents,
    callsByDate,
    recentConversations: formattedRecentConversations
  };
});

// Helper function to process calls data for the chart
function processCallsForChart(calls: { start_time: string }[]) {
  // Create a map to store counts by date
  const dateMap = new Map();

  // Initialize the last 90 days with zero counts
  for (let i = 0; i < 90; i++) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    dateMap.set(dateStr, { date: dateStr, desktop: 0, mobile: 0 });
  }

  // Count calls by date
  calls.forEach(call => {
    const dateStr = call.start_time.split('T')[0];
    if (dateMap.has(dateStr)) {
      const entry = dateMap.get(dateStr);
      // For simplicity, we're putting all calls in the "desktop" category
      // In a real app, you might differentiate between mobile and desktop
      entry.desktop += 1;
      dateMap.set(dateStr, entry);
    }
  });

  // Convert map to array and sort by date
  return Array.from(dateMap.values())
    .sort((a, b) => a.date.localeCompare(b.date));
}

export function getTemplates() {

  return [
    {
      id: 'blank',
      title: 'Blank',
      description: 'A blank template',
      icon: '💼',
      conversation_config:{
        "agent":{
          "prompt":{
            "prompt":"You are a helpful assistant.",
            "llm":"gemini-2.0-flash-001",
            "first_message": "",
          }
        }
      }
    },
    {
      id: 'sales',
      title: 'Sales Agent',
      description: 'An agent specialized in handling sales inquiries and converting leads.',
      icon: '💼',
      conversation_config:{
        "agent":{
          "prompt":{
            "prompt":"You are a helpful sales assistant.",
            "llm":"gemini-2.0-flash-001",
            "first_message": "",
          }
        }
      }
    },
    {
      id: 'support',
      title: 'Support Agent',
      description: 'Technical support specialist for handling customer issues.',
      icon: '🛠️',
      conversation_config:{
        "agent":{
          "prompt":{
            "prompt":"You are a helpful support assistant.",
            "llm":"gemini-2.0-flash-001",
            "first_message": "",
          }
        }
      }
    },
    {
      id: 'customer-service',
      title: 'Customer Service',
      description: 'General customer service agent for inquiries and assistance.',
      icon: '👥',
      conversation_config:{
        "agent":{
          "prompt":{
            "prompt":"You are a customer service assistant.",
            "llm":"gemini-2.0-flash-001",
            "first_message": "",
          }
        }
      }
    },
  ]
}
