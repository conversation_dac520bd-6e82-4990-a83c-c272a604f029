export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      agent_budget_limits: {
        Row: {
          agent_id: string | null
          daily_limit_cents: number | null
          id: string
          monthly_limit_cents: number | null
          updated_at: string | null
          weekly_limit_cents: number | null
        }
        Insert: {
          agent_id?: string | null
          daily_limit_cents?: number | null
          id?: string
          monthly_limit_cents?: number | null
          updated_at?: string | null
          weekly_limit_cents?: number | null
        }
        Update: {
          agent_id?: string | null
          daily_limit_cents?: number | null
          id?: string
          monthly_limit_cents?: number | null
          updated_at?: string | null
          weekly_limit_cents?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_budget_limits_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: true
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_configs: {
        Row: {
          agent_id: string | null
          config: Json | null
          config_type: string
          custom_metadata: Json | null
          external_provider_id: string | null
          id: string
        }
        Insert: {
          agent_id?: string | null
          config?: Json | null
          config_type: string
          custom_metadata?: Json | null
          external_provider_id?: string | null
          id?: string
        }
        Update: {
          agent_id?: string | null
          config?: Json | null
          config_type?: string
          custom_metadata?: Json | null
          external_provider_id?: string | null
          id?: string
        }
        Relationships: [
          {
            foreignKeyName: "agent_configs_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      agent_teams: {
        Row: {
          budget_cents: number | null
          created_at: string | null
          id: string
          name: string | null
          organization_id: string | null
        }
        Insert: {
          budget_cents?: number | null
          created_at?: string | null
          id?: string
          name?: string | null
          organization_id?: string | null
        }
        Update: {
          budget_cents?: number | null
          created_at?: string | null
          id?: string
          name?: string | null
          organization_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agent_teams_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      agents: {
        Row: {
          billing_strategy: string | null
          budget_cents: number | null
          created_at: string | null
          id: string
          name: string | null
          organization_id: string | null
          per_second_cost_cents: number | null
          team_id: string | null
        }
        Insert: {
          billing_strategy?: string | null
          budget_cents?: number | null
          created_at?: string | null
          id?: string
          name?: string | null
          organization_id?: string | null
          per_second_cost_cents?: number | null
          team_id?: string | null
        }
        Update: {
          billing_strategy?: string | null
          budget_cents?: number | null
          created_at?: string | null
          id?: string
          name?: string | null
          organization_id?: string | null
          per_second_cost_cents?: number | null
          team_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agents_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agents_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "agent_teams"
            referencedColumns: ["id"]
          },
        ]
      }
      conversation_messages: {
        Row: {
          conversation_id: string
          created_at: string | null
          id: number
          message: string
          meta: Json | null
          sender_id: string | null
          sender_type: string
        }
        Insert: {
          conversation_id: string
          created_at?: string | null
          id?: number
          message: string
          meta?: Json | null
          sender_id?: string | null
          sender_type: string
        }
        Update: {
          conversation_id?: string
          created_at?: string | null
          id?: number
          message?: string
          meta?: Json | null
          sender_id?: string | null
          sender_type?: string
        }
        Relationships: [
          {
            foreignKeyName: "conversation_messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          agent_id: string
          analysis: Json | null
          cost_cents: number | null
          created_at: string | null
          duration_secs: number | null
          id: string
          metadata: Json | null
          start_time: string | null
          status: string | null
          success_status: string | null
          summary: string | null
        }
        Insert: {
          agent_id: string
          analysis?: Json | null
          cost_cents?: number | null
          created_at?: string | null
          duration_secs?: number | null
          id: string
          metadata?: Json | null
          start_time?: string | null
          status?: string | null
          success_status?: string | null
          summary?: string | null
        }
        Update: {
          agent_id?: string
          analysis?: Json | null
          cost_cents?: number | null
          created_at?: string | null
          duration_secs?: number | null
          id?: string
          metadata?: Json | null
          start_time?: string | null
          status?: string | null
          success_status?: string | null
          summary?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversations_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_transactions: {
        Row: {
          agent_id: string | null
          amount_cents: number | null
          conversation_id: string | null
          created_at: string | null
          description: string | null
          duration_seconds: number | null
          id: string
          organization_id: string | null
          team_id: string | null
          type: string | null
        }
        Insert: {
          agent_id?: string | null
          amount_cents?: number | null
          conversation_id?: string | null
          created_at?: string | null
          description?: string | null
          duration_seconds?: number | null
          id?: string
          organization_id?: string | null
          team_id?: string | null
          type?: string | null
        }
        Update: {
          agent_id?: string | null
          amount_cents?: number | null
          conversation_id?: string | null
          created_at?: string | null
          description?: string | null
          duration_seconds?: number | null
          id?: string
          organization_id?: string | null
          team_id?: string | null
          type?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_transactions_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_transactions_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_transactions_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "credit_transactions_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "agent_teams"
            referencedColumns: ["id"]
          },
        ]
      }
      credit_wallets: {
        Row: {
          balance_cents: number | null
          id: string
          organization_id: string | null
          updated_at: string | null
        }
        Insert: {
          balance_cents?: number | null
          id?: string
          organization_id?: string | null
          updated_at?: string | null
        }
        Update: {
          balance_cents?: number | null
          id?: string
          organization_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "credit_wallets_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: true
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      customers: {
        Row: {
          id: string
          stripe_customer_id: string | null
        }
        Insert: {
          id: string
          stripe_customer_id?: string | null
        }
        Update: {
          id?: string
          stripe_customer_id?: string | null
        }
        Relationships: []
      }
      live_sessions: {
        Row: {
          agent_id: string | null
          ended_at: string | null
          ended_reason: string | null
          id: string
          is_active: boolean | null
          last_charged_at: string | null
          organization_id: string | null
          per_second_cost_cents: number
          started_at: string | null
          team_id: string | null
          total_cost_cents: number | null
          total_duration_secs: number | null
        }
        Insert: {
          agent_id?: string | null
          ended_at?: string | null
          ended_reason?: string | null
          id?: string
          is_active?: boolean | null
          last_charged_at?: string | null
          organization_id?: string | null
          per_second_cost_cents: number
          started_at?: string | null
          team_id?: string | null
          total_cost_cents?: number | null
          total_duration_secs?: number | null
        }
        Update: {
          agent_id?: string | null
          ended_at?: string | null
          ended_reason?: string | null
          id?: string
          is_active?: boolean | null
          last_charged_at?: string | null
          organization_id?: string | null
          per_second_cost_cents?: number
          started_at?: string | null
          team_id?: string | null
          total_cost_cents?: number | null
          total_duration_secs?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "live_sessions_agent_id_fkey"
            columns: ["agent_id"]
            isOneToOne: false
            referencedRelation: "agents"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "live_sessions_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "live_sessions_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "agent_teams"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_memberships: {
        Row: {
          id: string
          joined_at: string | null
          organization_id: string | null
          role: string | null
          user_id: string | null
        }
        Insert: {
          id?: string
          joined_at?: string | null
          organization_id?: string | null
          role?: string | null
          user_id?: string | null
        }
        Update: {
          id?: string
          joined_at?: string | null
          organization_id?: string | null
          role?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organization_memberships_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "organization_memberships_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          created_at: string | null
          id: string
          name: string
          tenant_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          name: string
          tenant_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          name?: string
          tenant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "organizations_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      prices: {
        Row: {
          active: boolean | null
          currency: string | null
          description: string | null
          id: string
          interval: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count: number | null
          metadata: Json | null
          product_id: string | null
          trial_period_days: number | null
          type: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount: number | null
        }
        Insert: {
          active?: boolean | null
          currency?: string | null
          description?: string | null
          id: string
          interval?: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count?: number | null
          metadata?: Json | null
          product_id?: string | null
          trial_period_days?: number | null
          type?: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount?: number | null
        }
        Update: {
          active?: boolean | null
          currency?: string | null
          description?: string | null
          id?: string
          interval?: Database["public"]["Enums"]["pricing_plan_interval"] | null
          interval_count?: number | null
          metadata?: Json | null
          product_id?: string | null
          trial_period_days?: number | null
          type?: Database["public"]["Enums"]["pricing_type"] | null
          unit_amount?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "prices_product_id_fkey"
            columns: ["product_id"]
            isOneToOne: false
            referencedRelation: "products"
            referencedColumns: ["id"]
          },
        ]
      }
      products: {
        Row: {
          active: boolean | null
          description: string | null
          id: string
          image: string | null
          metadata: Json | null
          name: string | null
        }
        Insert: {
          active?: boolean | null
          description?: string | null
          id: string
          image?: string | null
          metadata?: Json | null
          name?: string | null
        }
        Update: {
          active?: boolean | null
          description?: string | null
          id?: string
          image?: string | null
          metadata?: Json | null
          name?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          cancel_at: string | null
          cancel_at_period_end: boolean | null
          canceled_at: string | null
          created: string
          current_period_end: string
          current_period_start: string
          ended_at: string | null
          id: string
          metadata: Json | null
          price_id: string | null
          quantity: number | null
          status: Database["public"]["Enums"]["subscription_status"] | null
          trial_end: string | null
          trial_start: string | null
          user_id: string
        }
        Insert: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created?: string
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id: string
          metadata?: Json | null
          price_id?: string | null
          quantity?: number | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          trial_end?: string | null
          trial_start?: string | null
          user_id: string
        }
        Update: {
          cancel_at?: string | null
          cancel_at_period_end?: boolean | null
          canceled_at?: string | null
          created?: string
          current_period_end?: string
          current_period_start?: string
          ended_at?: string | null
          id?: string
          metadata?: Json | null
          price_id?: string | null
          quantity?: number | null
          status?: Database["public"]["Enums"]["subscription_status"] | null
          trial_end?: string | null
          trial_start?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "subscriptions_price_id_fkey"
            columns: ["price_id"]
            isOneToOne: false
            referencedRelation: "prices"
            referencedColumns: ["id"]
          },
        ]
      }
      team_budget_limits: {
        Row: {
          daily_limit_cents: number | null
          id: string
          monthly_limit_cents: number | null
          team_id: string | null
          updated_at: string | null
          weekly_limit_cents: number | null
        }
        Insert: {
          daily_limit_cents?: number | null
          id?: string
          monthly_limit_cents?: number | null
          team_id?: string | null
          updated_at?: string | null
          weekly_limit_cents?: number | null
        }
        Update: {
          daily_limit_cents?: number | null
          id?: string
          monthly_limit_cents?: number | null
          team_id?: string | null
          updated_at?: string | null
          weekly_limit_cents?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "team_budget_limits_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: true
            referencedRelation: "agent_teams"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_admins: {
        Row: {
          created_at: string | null
          id: string
          tenant_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          tenant_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          tenant_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_admins_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_admins_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      tenant_users: {
        Row: {
          created_at: string | null
          id: string
          is_primary: boolean | null
          tenant_id: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          tenant_id?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_primary?: boolean | null
          tenant_id?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tenant_users_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "tenant_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      tenants: {
        Row: {
          created_at: string | null
          domain: string
          id: string
          name: string
          settings: Json | null
          slug: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          domain: string
          id?: string
          name: string
          settings?: Json | null
          slug: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          domain?: string
          id?: string
          name?: string
          settings?: Json | null
          slug?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          avatar_url: string | null
          billing_address: Json | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          metadata: Json | null
          payment_method: Json | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          billing_address?: Json | null
          created_at?: string
          email?: string
          full_name?: string | null
          id: string
          metadata?: Json | null
          payment_method?: Json | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          billing_address?: Json | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          metadata?: Json | null
          payment_method?: Json | null
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      add_user_to_tenant: {
        Args: {
          user_id: string
          tenant_id: string
          is_primary?: boolean
        }
        Returns: string
      }
      assign_user_to_tenant: {
        Args: {
          user_id: string
          tenant_id: string
        }
        Returns: string
      }
      can_start_call: {
        Args: {
          agent: string
          estimated_cost: number
        }
        Returns: boolean
      }
      charge_live_sessions: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      create_organization:
        | {
            Args: {
              org_name: string
            }
            Returns: string
          }
        | {
            Args: {
              org_name: string
              tenant_id?: string
            }
            Returns: string
          }
      create_tenant: {
        Args: {
          tenant_name: string
          tenant_slug: string
          tenant_domain: string
        }
        Returns: string
      }
      create_user_with_org: {
        Args: {
          user_id: string
          email: string
          full_name: string
          avatar_url: string
        }
        Returns: undefined
      }
      finalize_call: {
        Args: {
          agent: string
          cost: number
          conversation: string
          duration_secs?: number
        }
        Returns: boolean
      }
      find_orphaned_organizations: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          name: string
          created_at: string
        }[]
      }
      get_elevenlabs_config: {
        Args: {
          config_id: string
        }
        Returns: Json
      }
      insert_conversation_with_messages: {
        Args: {
          _id: string
          _agent_id: string
          _status: string
          _start_time: string
          _duration_secs: number
          _cost_cents: number
          _summary: string
          _success_status: string
          _transcript: Json
          _metadata: Json
          _analysis: Json
        }
        Returns: undefined
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_member: {
        Args: {
          org_id: string
        }
        Returns: boolean
      }
      is_member_or_tenant_admin: {
        Args: {
          org_id: string
        }
        Returns: boolean
      }
      is_tenant_admin: {
        Args: {
          tenant_id: string
        }
        Returns: boolean
      }
      is_tenant_user: {
        Args: {
          tenant_id: string
        }
        Returns: boolean
      }
    }
    Enums: {
      pricing_plan_interval: "day" | "week" | "month" | "year"
      pricing_type: "one_time" | "recurring"
      subscription_status:
        | "trialing"
        | "active"
        | "canceled"
        | "incomplete"
        | "incomplete_expired"
        | "past_due"
        | "unpaid"
        | "paused"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof PublicSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof PublicSchema["CompositeTypes"]
    ? PublicSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

