'use client'

import { useState, useEffect } from 'react'
import { useActionState } from 'react'

import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'

import { AgentGeneralTab } from './tabs/general-tab'
import { AgentVoiceTab } from './tabs/voice-tab'
import { AgentWidgetTab } from './tabs/widget-tab'

import { updateAgent } from '@/actions/serverActions'
import { Database } from '@/types_db'

type AgentConfig = Database['public']['Tables']['agent_configs']['Row']

import { DeleteAgentButton } from '../delete-agent-button'
import { DuplicateAgentButton } from '../duplicate-agent-button'

import { WidgetProvider, defaultWidgetSettings, WidgetSettings } from '@/contexts/widget-context'

// Props
interface AgentSettingsSidebarProps {
  agentId: string
  orgId: string
  agentData: any
  elevenLabsConfig: AgentConfig | null
  internalConfig: AgentConfig | null
}

export function AgentSettingsSidebar({ agentId, orgId, agentData, elevenLabsConfig }: AgentSettingsSidebarProps) {
  const [isDirty, setIsDirty] = useState(false)
  const [initialWidgetSettings, setInitialWidgetSettings] = useState<WidgetSettings>(defaultWidgetSettings)

  // useActionState for server form submission
  const initialState = { success: false, error: '', id: '' }
  const [formState, formAction, isPending] = useActionState(updateAgent, initialState)

  // Reset dirty state on successful save
  useEffect(() => {
    if (formState.success) setIsDirty(false)
  }, [formState.success])

  // Helper to get config values from the config JSONB field
  const getConfigValue = (config: AgentConfig | null | undefined, path: string, defaultValue: any): any => {
    if (!config || !config.config) return defaultValue
    const configData = config.config as Record<string, any>
    const keys = path.split('.')
    let value = configData
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key]
      } else {
        return defaultValue
      }
    }
    return value !== undefined ? value : defaultValue
  }

  // Voice settings
  const voiceSettings = {
    // Voice ID from ElevenLabs config or internal config
    voice: getConfigValue(elevenLabsConfig, 'tts_config.voice_id', ''),

    // Voice language - from voice config or agent language
    voiceLanguage: getConfigValue(elevenLabsConfig, 'tts_config.voice_language', ''),

    latencyOptimization: getConfigValue(elevenLabsConfig, 'tts_config.optimize_streaming_latency', 3),
    stability: getConfigValue(elevenLabsConfig, 'tts_config.stability', 0.5),
    similarity: getConfigValue(elevenLabsConfig, 'tts_config.similarity_boost', 0.75)
  }

  // Widget settings - use internal config for widget metadata
  useEffect(() => {
    if (elevenLabsConfig?.custom_metadata) {
      try {
        const metadata = elevenLabsConfig.custom_metadata as Record<string, any>
        if (metadata.widget) {
          setInitialWidgetSettings(prev => ({
            ...prev,
            position: metadata.widget.position ?? prev.position,
            customText: metadata.widget.customText ?? prev.customText,
            hideIcon: metadata.widget.hideIcon ?? prev.hideIcon,
            customIcon: metadata.widget.customIcon ?? prev.customIcon,
            customCSS: metadata.widget.customCSS ?? prev.customCSS,
            widgetType: metadata.widget.widgetType ?? prev.widgetType,
          }))
        }
      } catch (error) {
        console.error('Error parsing widget settings:', error)
      }
    }
  }, [elevenLabsConfig])

  return (
    <div className="h-full">
      <div className="p-4">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">{agentData?.name || 'Agent Settings'}</h2>
          <div className="flex gap-2">
            <DuplicateAgentButton
              agentId={agentId}
              orgId={orgId}
              agentName={agentData?.name || 'Agent'}
              size="sm"
              variant="outline"
            />
            <DeleteAgentButton
              agentId={agentId}
              orgId={orgId}
              agentName={agentData?.name || 'Agent'}
              size="sm"
            />
          </div>
        </div>
        <p className="text-sm text-muted-foreground mb-4">Configure your agent settings</p>
      </div>

      <ScrollArea className="h-[calc(100vh-120px)]">
        <form action={formAction} className="px-4 pb-16">
          <input type="hidden" name="agentId" value={agentId} />
          <input type="hidden" name="orgId" value={orgId} />

          <Tabs defaultValue="agent" className="w-full">
            <TabsList className="mb-4 w-full">
              <TabsTrigger value="agent" className="flex-1">Agent</TabsTrigger>
              <TabsTrigger value="voice" className="flex-1">Voice</TabsTrigger>
              <TabsTrigger value="widget" className="flex-1">Widget</TabsTrigger>
            </TabsList>

            <TabsContent value="agent" className='focus:ring-0'>
              <AgentGeneralTab
                setIsDirty={setIsDirty}
                initialValues={{
                  name: agentData?.name,
                  teamId: agentData?.team_id,
                  budget_cents: agentData?.budget_cents,
                  defaultLanguage: getConfigValue(elevenLabsConfig, 'language', 'en'),
                  firstMessage: getConfigValue(elevenLabsConfig, 'first_message', ''),
                  systemPrompt: getConfigValue(elevenLabsConfig, 'prompt_config.prompt', ''),
                  llmProvider: getConfigValue(elevenLabsConfig, 'prompt_config.llm', 'gpt-4'),
                  temperature: getConfigValue(elevenLabsConfig, 'prompt_config.temperature', 0.7),
                  tokenLimit: getConfigValue(elevenLabsConfig, 'prompt_config.max_tokens', -1),
                }}
              />
            </TabsContent>

            <TabsContent value="voice" className='focus:ring-0'>
              <AgentVoiceTab
                setIsDirty={setIsDirty}
                initialValues={{
                  voice: voiceSettings.voice,
                  voiceLanguage: voiceSettings.voiceLanguage || 'en',
                  latencyOptimization: voiceSettings.latencyOptimization,
                  stability: voiceSettings.stability,
                  similarity: voiceSettings.similarity
                }}
              />
            </TabsContent>

            <TabsContent value="widget" className='focus:ring-0'>
              <WidgetProvider initialSettings={initialWidgetSettings}>
                <AgentWidgetTab setIsDirty={setIsDirty} agentId={agentId} />
              </WidgetProvider>
            </TabsContent>
          </Tabs>

          {isDirty && (
            <div className="fixed bottom-0 left-0 right-0 bg-background border-t p-4 flex items-center justify-between z-10">
              <span>You have unsaved changes</span>
              <div className="space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsDirty(false)}>
                  Clear
                </Button>
                <Button type="submit" disabled={isPending}>
                  {isPending ? 'Saving...' : 'Save'}
                </Button>
              </div>
            </div>
          )}

          {formState.error && (
            <p className="text-red-500 mt-2">{formState.error}</p>
          )}
          {formState.success && (
            <p className="text-green-600 mt-2">Saved successfully!</p>
          )}
        </form>
      </ScrollArea>
    </div>
  )
}
